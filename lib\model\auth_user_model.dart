class UserModel {
    final bool status;
    final String message;
    final Data data;
    final Errors errors;
    final String apiToken;

    UserModel({
        required this.status,
        required this.message,
        required this.data,
        required this.errors,
        required this.apiToken,
    });

}

class Data {
    final int id;
    final String name;
    final String email;
    final String phone;
    final dynamic dob;
    final int rating;
    final dynamic image;
    final String otp;
    final dynamic barLicenseNo;
    final dynamic practiceState;
    final dynamic experience;
    final dynamic organization;
    final dynamic lawSchool;
    final dynamic degree;
    final dynamic graduationYear;
    final dynamic address;
    final dynamic bio;
    final dynamic languages;
    final int isPersonalInfo;
    final int isLegalExperience;
    final int isEducation;
    final int isBusiness;
    final int isGovIdUploaded;
    final int isBaridUploaded;
    final int isSelfie;
    final int isOptionalDetails;
    final String apiToken;
    final dynamic fcmToken;
    final int isOtpVerified;
    final int isNotified;
    final int isMilestoneNotified;
    final int isPayoutNotified;
    final int isCaseNotified;
    final int isVerified;
    final int isProfileCompleted;
    final dynamic providerId;
    final dynamic providerName;
    final int status;
    final dynamic emailVerifiedAt;
    final String password;
    final dynamic rememberToken;
    final String createdAt;
    final String updatedAt;
    final dynamic deletedAt;

    Data({
        required this.id,
        required this.name,
        required this.email,
        required this.phone,
        required this.dob,
        required this.rating,
        required this.image,
        required this.otp,
        required this.barLicenseNo,
        required this.practiceState,
        required this.experience,
        required this.organization,
        required this.lawSchool,
        required this.degree,
        required this.graduationYear,
        required this.address,
        required this.bio,
        required this.languages,
        required this.isPersonalInfo,
        required this.isLegalExperience,
        required this.isEducation,
        required this.isBusiness,
        required this.isGovIdUploaded,
        required this.isBaridUploaded,
        required this.isSelfie,
        required this.isOptionalDetails,
        required this.apiToken,
        required this.fcmToken,
        required this.isOtpVerified,
        required this.isNotified,
        required this.isMilestoneNotified,
        required this.isPayoutNotified,
        required this.isCaseNotified,
        required this.isVerified,
        required this.isProfileCompleted,
        required this.providerId,
        required this.providerName,
        required this.status,
        required this.emailVerifiedAt,
        required this.password,
        required this.rememberToken,
        required this.createdAt,
        required this.updatedAt,
        required this.deletedAt,
    });

}

class Errors {
    Errors();
}
