class NewUser {
    final bool status;
    final String message;
    final Data data;
    final Errors errors;
    final String apiToken;
    final int otp;

    NewUser({
        required this.status,
        required this.message,
        required this.data,
        required this.errors,
        required this.apiToken,
        required this.otp,
    });

}

class Data {
    final String name;
    final String email;
    final String phone;
    final String password;
    final int otp;
    final String updatedAt;
    final String createdAt;
    final int id;
    final String apiToken;

    Data({
        required this.name,
        required this.email,
        required this.phone,
        required this.password,
        required this.otp,
        required this.updatedAt,
        required this.createdAt,
        required this.id,
        required this.apiToken,
    });

}

class Errors {
    Errors();
}
